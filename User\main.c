/* 头文件引用 */
#include "mydefine.h"


/* 主函数 */
int main(void)
{
	/* 默认初始化 */
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
	SystemCoreClockUpdate();
	USART_Printf_Init(115200);
	Delay_Init();
	
	/* 自定义初始化 */
	rt_ringbuffer_init(&uart_ringbuffer_1, ringbuffer_pool_1, sizeof(ringbuffer_pool_1));
	rt_ringbuffer_init(&uart_ringbuffer_2, ringbuffer_pool_2, sizeof(ringbuffer_pool_2));
	rt_ringbuffer_init(&uart_ringbuffer_3, ringbuffer_pool_3, sizeof(ringbuffer_pool_3));

	SG90_Init();	// 舵机初始化
	Key_Init();		// 矩阵按键初始化
	Audio_Init();	// 语音播报串口初始化
	rfid_Init();	// 刷卡模块初始化
	TFT_LCD_Init(); // LCD彩屏初始化动画
	AS608_Init();	// 指纹模块初始化


	esp8266_Init();	// esp8266通信串口初始化
	Delay_Ms(1000);

	onenet_Init();
	/* 调度器初始化 */
	scheduler_init();

	/*死循环*/
	while(1)
    {
        scheduler_run();
	}
}


#include "RFID_app.h"
#include "string.h"
#include <stdarg.h>
#include <stdio.h>


// 配置参数（根据实际硬件修改）
#define RX_BUF_SIZE     128                     // 缓冲区大小
#define DMA_CHANNEL     DMA1_Channel6           // CH32V307 USART2_RX对应DMA1_Channel6

uint8_t ringbuffer_pool_1[128];
uint8_t uart_rx_dma_buffer_1[128] = {0};// DMA直接接收缓冲区
struct rt_ringbuffer uart_ringbuffer_1;// 环形缓冲区
uint8_t uart_dma_buffer_1[128] = {0};// 用于处理的用户缓冲区
uint8_t rx_complete_flag = 0;                  // 接收完成标志


uint8_t id_flag = 0;
uint8_t id_index = 1; // 0 1 2
uint8_t id_index_old = 0; // 0 1 2

uint8_t card_count = 0;
uint8_t card_enable = 1;

uint8_t rfid_card[3][12] = {
    {0x04, 0x0C, 0x02, 0x30, 0x00, 0x04, 0x00, 0x49, 0xA5, 0x46, 0xAB, 0xC0},
    {},
    {}
};


/**
 * @brief  重启DMA接收
 */
void USART_DMA_Receive_Restart(void)
{
    DMA_Cmd(DMA_CHANNEL, DISABLE);                  // 关闭DMA
    DMA_SetCurrDataCounter(DMA_CHANNEL, RX_BUF_SIZE); // 重置计数
    memset(uart_rx_dma_buffer_1, 0, RX_BUF_SIZE);     // 清空DMA缓冲区
    DMA_Cmd(DMA_CHANNEL, ENABLE);                   // 使能DMA
}


/**
 * @brief  清除串口空闲中断标志（解决标准库无此函数的问题）
 * @param  USARTx: 目标串口
 */
void USART_Clear_IdleFlag_1(USART_TypeDef* USARTx)
{
    // 读取SR寄存器后再读取DR寄存器可清除空闲标志
    (void)USARTx->STATR;
    (void)USARTx->DATAR;
}


void Usart2_Init(void)
{

    GPIO_InitTypeDef  GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef  NVIC_InitStructure;
    DMA_InitTypeDef DMA_InitStructure;

    // 1. 使能时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE); // 改为GPIOA
    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_DMA1, ENABLE);     // 使能DMA时钟

    // 2. 配置GPIO（TX=PA2，RX=PA3）
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;// TX引脚：复用推挽输出
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;// RX引脚：浮空输入（或上拉输入）
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; // 输入模式
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 3. 配置USART2
    USART_InitStructure.USART_BaudRate = 115200; // 建议用标准波特率，需确保双方一致
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(USART2, &USART_InitStructure);

    // 4. 配置NVIC（串口中断）
    NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 5. 使能中断（仅使能空闲中断，关闭RXNE中断）
    USART_ITConfig(USART2, USART_IT_IDLE, ENABLE);  // 使能空闲中断
    USART_ITConfig(USART2, USART_IT_RXNE, DISABLE); // 禁用RXNE中断（与DMA冲突）

    // 6. 配置DMA接收
    DMA_DeInit(DMA_CHANNEL);
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&(USART2->DATAR);
    DMA_InitStructure.DMA_MemoryBaseAddr = (uint32_t)uart_rx_dma_buffer_1;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
    DMA_InitStructure.DMA_BufferSize = RX_BUF_SIZE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Normal;
    DMA_InitStructure.DMA_Priority = DMA_Priority_Medium;
    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
    DMA_Init(DMA_CHANNEL, &DMA_InitStructure);

    // 7. 配置DMA中断（传输完成中断）
    DMA_ITConfig(DMA_CHANNEL, DMA_IT_HT, DISABLE);  // 关闭半满中断
    DMA_ITConfig(DMA_CHANNEL, DMA_IT_TC, ENABLE);   // 使能传输完成中断
    DMA_ITConfig(DMA_CHANNEL, DMA_IT_TE, ENABLE);   // 使能传输错误中断（用于调试）

    NVIC_InitStructure.NVIC_IRQChannel = DMA1_Channel6_IRQn; // DMA中断通道
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 8. 启动USART，启动DMA和关联USART
    USART_Cmd(USART2, ENABLE);
    USART_DMACmd(USART2, USART_DMAReq_Rx, ENABLE); // 关联DMA接收
    DMA_Cmd(DMA_CHANNEL, ENABLE);

    // 9. 清除可能存在的标志位
    USART_ClearFlag(USART2, USART_FLAG_IDLE);
    DMA_ClearFlag(DMA1_FLAG_TC6 | DMA1_FLAG_HT6 | DMA1_FLAG_TE6);
}



void USART2_IRQHandler(void)
{
    // 检查空闲中断
    if(USART_GetITStatus(USART2, USART_IT_IDLE) == SET)
    {
        USART_Clear_IdleFlag_1(USART2); // 清除空闲标志
        DMA_Cmd(DMA_CHANNEL, DISABLE); // 关闭DMA

        // 计算本次接收长度：缓冲区总大小 - DMA剩余计数
        uint16_t recv_len = RX_BUF_SIZE - DMA_GetCurrDataCounter(DMA_CHANNEL);

        if(recv_len > 0)
        {
            // 将DMA接收的数据存入环形缓冲区
            rt_ringbuffer_put(&uart_ringbuffer_1, uart_rx_dma_buffer_1, recv_len);

            // 调试信息：可以在这里添加LED指示或其他调试手段
            // 注意：在中断中不要使用printf等可能阻塞的函数
        }
        USART_DMA_Receive_Restart(); // 重启DMA接收
    }
    // 检查其他可能的中断标志
    if(USART_GetITStatus(USART2, USART_IT_RXNE) == SET)
    {
        // 如果RXNE中断意外触发，清除它
        USART_ClearITPendingBit(USART2, USART_IT_RXNE);
        volatile uint8_t dummy = USART_ReceiveData(USART2); // 读取数据寄存器
        (void)dummy; // 避免编译器警告
    }
}



/**
 * @brief  DMA1_Channel6中断处理（缓冲区满或传输错误）
 */
void DMA1_Channel6_IRQHandler(void)
{
    // 传输完成中断
    if(DMA_GetITStatus(DMA1_IT_TC6) == SET)
    {
        DMA_ClearITPendingBit(DMA1_IT_TC6); // 清除传输完成标志
        DMA_Cmd(DMA_CHANNEL, DISABLE);
        // 缓冲区满，接收长度为RX_BUF_SIZE
        rt_ringbuffer_put(&uart_ringbuffer_1, uart_rx_dma_buffer_1, RX_BUF_SIZE);
        USART_DMA_Receive_Restart(); // 重启DMA接收
    }
    // 传输错误中断（用于调试）
    if(DMA_GetITStatus(DMA1_IT_TE6) == SET)
    {
        DMA_ClearITPendingBit(DMA1_IT_TE6); // 清除传输错误标志
        USART_DMA_Receive_Restart(); // 重启DMA接收
    }
}

void rfid_Init()
{
    Usart2_Init();
}

void rfid_task(void)
{
    //刷卡失败次数过多 锁60s
    if(card_count > 3)
    {
        static uint32_t nowtime4;
        static uint8_t tick_flag4 = 0;
        if(tick_flag4 == 0)
        {
            audio_disp(11);             //....输入错误次数过多 60秒后重新输入
            LCD_Fill(0,30,96,53,RED);   // 填充提示
            key_enable = 0;             // 禁用按键
            card_enable = 0;            // 禁用卡片
            finger_enable = 0;          // 禁用指纹

            nowtime4 = uwTick;
            tick_flag4 = 1;
        }
        else if((uwTick - nowtime4 >= 15000) && (tick_flag4 == 1))
        {
            audio_disp(2);              //....请输入密码
            LCD_Fill(0,30,96,53,GBLUE);
            card_enable = 1;
            key_enable = 1;
            finger_enable = 1;

            card_count = 0;
            tick_flag4 = 0;
        }
    }
    if(card_enable == 0) return;

    uint16_t recv_len = rt_ringbuffer_data_len(&uart_ringbuffer_1);
    if (recv_len == 0) return;

    if(recv_len > 0 && (recv_len <= RX_BUF_SIZE) && card_enable == 1)
    {    
        rt_ringbuffer_get(&uart_ringbuffer_1, uart_dma_buffer_1, recv_len);
        if(cmd_enable == 2 && confirm_flag == confirm_cmd)//录入卡片
        {
            for(uint8_t i=0;i<12;i++)
            {
                rfid_card[id_index][i] = uart_dma_buffer_1[i];
            }
            ++id_index;
            id_index %= 3;
            audio_disp(14);//....新卡已录入
            LCD_ShowChinese(0,0,"门已上锁",BLACK,WHITE,24,0);
            cmd_enable = cmd_card_enable_old = 0;
            confirm_flag = confirm_idle;
        }
        else if(cmd_enable == 0 && confirm_flag == confirm_idle)//刷卡解锁
        {
            for(uint8_t j=0;j<3 ;j++)
            {
                id_flag = 1;
                for(uint8_t i=0;i<12;i++)
                {
                    if(uart_dma_buffer_1[i] != rfid_card[j][i])
                    {
                        id_flag = 0;
                        break;
                    }
                }
                if(id_flag == 1) break;
            }

            if(id_flag)
            {
                lock(0);
                id_flag = 0;
                card_count = 0;
            }
            else
            {
                card_count++;
                if(card_count > 3) audio_disp(7); //....刷卡失败次数过多 请60秒后重新刷卡
                else audio_disp(4);//....刷卡失败
            }
        }
        memset(uart_dma_buffer_1, 0, RX_BUF_SIZE); // 清空处理缓冲区
    }

}











/**
 * @brief  自定义printf函数，通过CH32的USART发送格式化字符串
 * @param  USARTx: 串口外设指针（如USART1, USART2等）
 * @param  format: 格式化字符串
 * @param  ...: 可变参数列表
 * @return 发送的字符数，失败返回-1
 */
int my_printf(USART_TypeDef *USARTx, const char *format, ...)
{
    char buffer[1024];  // 临时缓冲区，可根据需求调整大小
    va_list arg;
    int len;
    // 处理可变参数，格式化字符串到缓冲区
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    // 检查格式化结果
    if (len < 0 || len >= sizeof(buffer))
    {
        return -1;  // 格式化失败或缓冲区溢出
    }
    // 通过CH32标准库函数发送数据
    for (int i = 0; i < len; i++)
    {
        // 等待发送缓冲区为空
        while ((USARTx->STATR & USART_STATR_TXE) == 0);
        // 发送一个字节
        USARTx->DATAR = (uint8_t)buffer[i];
    }
    // 等待最后一个字节发送完成（可选，根据需求决定）
    while ((USARTx->STATR & USART_STATR_TC) == 0);
    return len;
}
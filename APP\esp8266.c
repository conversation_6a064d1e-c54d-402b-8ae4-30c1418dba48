#include "esp8266.h"
#include "string.h"
#include <stdarg.h>
#include <stdio.h>

#define RX_BUF_SIZE     128                     // 缓冲区大小
#define DMA_CHANNEL_3     DMA2_Channel7           // CH32V307 UART7_RX对应DMA2_Channel9

uint8_t uart_rx_dma_buffer_3[128];
struct rt_ringbuffer uart_ringbuffer_3;
uint8_t ringbuffer_pool_3[128];
uint8_t uart_dma_buffer_3[128] = {0};// 用于处理的用户缓冲区


char string0[] = "AT+RST\r\n";
char string1[] = "AT+CWMODE=1\r\n";
char string2[] = "AT+CWDHCP=1,1\r\n";
char string3[] = "AT+CWJAP=\"LINGYUHAN\",\"12345678\"\r\n";
char string4[] = "AT+MQTTUSERCFG=0,1,\"ch32\",\"66KfCdp1A7\",\"version=2018-10-31&res=products%%2F66KfCdp1A7%%2Fdevices%%2Fch32&et=1788230366&method=md5&sign=WPSJNJLlqogruTwHPd6DmQ%%3D%%3D\",0,0,\"\"\r\n";
char string5[] = "AT+MQTTCONN=0,\"mqtts.heclouds.com\",1883,1\r\n";
char string6[] = "AT+MQTTSUB=0,\"$sys/66KfCdp1A7/ch32/thing/property/set\",1\r\n";

char string8[] = "AT+MQTTPUB=0,\"$sys/66KfCdp1A7/ch32/thing/property/post\",\"{\\\"id\\\":\\\"123\\\"\\,\\\"params\\\":{\\\"appkey\\\":{\\\"value\\\":67},\\\"appstring\\\":{\\\"value\\\":\\\"hello\\\"}}}\",0,0\r\n";
char string9[] = "AT+MQTTPUB=0,\"$sys/56HeNPU4AK/ch32/thing/property/post\",\"{\\\"id\\\":\\\"123\\\"\\,\\\"params\\\":{\\\"appkey\\\":{\\\"value\\\":55}}}\",0,0\r\n";
char string10[] = "AT+MQTTPUB=0,\"$sys/56HeNPU4AK/ch32/thing/property/post\",\"{\\\"id\\\":\\\"123\\\"\\,\\\"params\\\":{\\\"appstring\\\":{\\\"value\\\":\\\"hello\\\"}}}\",0,0\r\n";

/**
 * @brief  重启DMA接收
 */
void USART_DMA_Receive_Restart_3()
{
    DMA_Cmd(DMA_CHANNEL_3, DISABLE);                  // 关闭DMA
    DMA_SetCurrDataCounter(DMA_CHANNEL_3, RX_BUF_SIZE); // 重置计数
    memset(uart_rx_dma_buffer_3, 0, RX_BUF_SIZE);     // 清空DMA缓冲区
    DMA_Cmd(DMA_CHANNEL_3, ENABLE);                   // 使能DMA
}


/**
 * @brief  清除串口空闲中断标志（解决标准库无此函数的问题）
 * @param  USARTx: 目标串口
 */
void USART_Clear_IdleFlag_3(USART_TypeDef* USARTx)
{
    // 读取SR寄存器后再读取DR寄存器可清除空闲标志
    (void)USARTx->STATR;
    (void)USARTx->DATAR;
}


void Uart6_Init()
{
    GPIO_InitTypeDef  GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef  NVIC_InitStructure;
    DMA_InitTypeDef DMA_InitStructure;

    RCC_APB1PeriphClockCmd(RCC_APB1Periph_UART6 , ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC , ENABLE);
    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_DMA2, ENABLE);      // 使能DMA时钟

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(GPIOC, &GPIO_InitStructure);
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;// RX引脚：浮空输入（或上拉输入）
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; // 输入模式
    GPIO_Init(GPIOC, &GPIO_InitStructure);

    USART_InitStructure.USART_BaudRate = 115200;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx|USART_Mode_Rx;
    USART_Init(UART6, &USART_InitStructure);
    
    NVIC_InitStructure.NVIC_IRQChannel = UART6_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 5. 使能中断（仅使能空闲中断，关闭RXNE中断）
    USART_ITConfig(UART6, USART_IT_IDLE, ENABLE);  // 使能空闲中断
    USART_ITConfig(UART6, USART_IT_RXNE, DISABLE); // 禁用RXNE中断（与DMA冲突）

    // 6. 配置DMA接收
    DMA_DeInit(DMA_CHANNEL_3);
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&(UART6->DATAR);
    DMA_InitStructure.DMA_MemoryBaseAddr = (uint32_t)uart_rx_dma_buffer_3;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
    DMA_InitStructure.DMA_BufferSize = RX_BUF_SIZE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Normal;
    DMA_InitStructure.DMA_Priority = DMA_Priority_Medium;
    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
    DMA_Init(DMA_CHANNEL_3, &DMA_InitStructure);

    // 7. 配置DMA中断（传输完成中断）
    DMA_ITConfig(DMA_CHANNEL_3, DMA_IT_HT, DISABLE);  // 关闭半满中断
    DMA_ITConfig(DMA_CHANNEL_3, DMA_IT_TC, ENABLE);   // 使能传输完成中断
    DMA_ITConfig(DMA_CHANNEL_3, DMA_IT_TE, ENABLE);   // 使能传输错误中断（用于调试）

    NVIC_InitStructure.NVIC_IRQChannel = DMA2_Channel7_IRQn; // DMA中断通道
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 8. 启动USART，启动DMA和关联USART
    USART_Cmd(UART6, ENABLE);
    USART_DMACmd(UART6, USART_DMAReq_Rx, ENABLE); // 关联DMA接收
    DMA_Cmd(DMA_CHANNEL_3, ENABLE);

    // 9. 清除可能存在的标志位
    USART_ClearFlag(UART6, USART_FLAG_IDLE);
    USART_ClearFlag(UART6,USART_FLAG_TC);//清空串口3的发送标志位
    DMA_ClearFlag(DMA2_FLAG_TC7 | DMA2_FLAG_HT7 | DMA2_FLAG_TE7);
}


void UART6_IRQHandler(void)
{
    if(USART_GetITStatus(UART6, USART_IT_IDLE) == SET)
    {
        USART_Clear_IdleFlag_3(UART6); // 清除空闲标志
        DMA_Cmd(DMA_CHANNEL_3, DISABLE); // 关闭DMA

        // 计算本次接收长度
        uint16_t recv_len = RX_BUF_SIZE - DMA_GetCurrDataCounter(DMA_CHANNEL_3);

        if(recv_len > 0)
        {
            // 将DMA接收的数据存入环形缓冲区
            rt_ringbuffer_put(&uart_ringbuffer_3, uart_rx_dma_buffer_3, recv_len);
        }
        
        // 重启DMA接收
        USART_DMA_Receive_Restart_3();
    }
}



/**
 * @brief  DMA2_Channel7中断处理（缓冲区满或传输错误）
 */
void DMA2_Channel7_IRQHandler(void)
{
    // 传输完成中断
    if(DMA_GetITStatus(DMA2_IT_TC7) == SET)
    {
        DMA_ClearITPendingBit(DMA2_IT_TC7);
        DMA_Cmd(DMA_CHANNEL_3, DISABLE);
        rt_ringbuffer_put(&uart_ringbuffer_3, uart_rx_dma_buffer_3, RX_BUF_SIZE);
        USART_DMA_Receive_Restart_3();
    }
    
    // 传输错误中断
    if(DMA_GetITStatus(DMA2_IT_TE7) == SET)
    {
        DMA_ClearITPendingBit(DMA2_IT_TE7);
        USART_DMA_Receive_Restart_3();
    }
}







void esp8266_Init()
{
    Uart6_Init();
}


void onenet_Init()
{
    my_printf(UART6,string0);
    Delay_Ms(1000);
    my_printf(UART6,string1);
    Delay_Ms(1000);
    my_printf(UART6,string2);
    Delay_Ms(1000);
    my_printf(UART6,string3);
    Delay_Ms(2000);
    my_printf(UART6,string4);
    Delay_Ms(2000);
    my_printf(UART6,string5);
    Delay_Ms(3000);
    my_printf(UART6,string6);
    Delay_Ms(3000);
    // 简化格式
    // my_printf(UART6, string10);
    // Delay_Ms(2000);
    // my_printf(UART6, string9);
    // Delay_Ms(2000);
}




void esp8266_task()
{
    uint16_t length;
    length = rt_ringbuffer_data_len(&uart_ringbuffer_3);
    if (length == 0) return;
    
    rt_ringbuffer_get(&uart_ringbuffer_3, uart_dma_buffer_3, length);

    // 输出接收到的数据
    my_printf(USART1, (const char *)uart_dma_buffer_3);


    
    // 清空接收缓冲区
    memset(uart_dma_buffer_3, 0, sizeof(uart_dma_buffer_3));
}

